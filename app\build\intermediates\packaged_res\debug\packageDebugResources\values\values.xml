<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="bottom_sheet_handle">#CAC4D0</color>
    <color name="md_theme_light_background">#FFFBFE</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_inverseOnSurface">#F4EFF4</color>
    <color name="md_theme_light_inversePrimary">#D0BCFF</color>
    <color name="md_theme_light_inverseSurface">#313033</color>
    <color name="md_theme_light_onBackground">#1C1B1F</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#21005D</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#1D192B</color>
    <color name="md_theme_light_onSurface">#1C1B1F</color>
    <color name="md_theme_light_onSurfaceVariant">#49454F</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#31111D</color>
    <color name="md_theme_light_outline">#79747E</color>
    <color name="md_theme_light_outlineVariant">#CAC4D0</color>
    <color name="md_theme_light_primary">#6750A4</color>
    <color name="md_theme_light_primaryContainer">#EADDFF</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_secondary">#625B71</color>
    <color name="md_theme_light_secondaryContainer">#E8DEF8</color>
    <color name="md_theme_light_shadow">#000000</color>
    <color name="md_theme_light_surface">#FFFBFE</color>
    <color name="md_theme_light_surfaceTint">#6750A4</color>
    <color name="md_theme_light_surfaceVariant">#E7E0EC</color>
    <color name="md_theme_light_tertiary">#7D5260</color>
    <color name="md_theme_light_tertiaryContainer">#FFD8E4</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success_green">#4CAF50</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning_orange">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="accessibility_service_description">Seeq自动化操作服务，用于自动操作欢律App</string>
    <string name="app_name">Seeq</string>
    <string name="automation_completed">自动化操作完成</string>
    <string name="automation_failed">自动化操作失败</string>
    <string name="automation_running">自动化操作正在运行...</string>
    <string name="check_permission">检查权限</string>
    <string name="eq_adjusting">正在调节EQ均衡器...</string>
    <string name="eq_completed">EQ调节完成</string>
    <string name="permission_granted">权限已开启</string>
    <string name="permission_required">需要开启无障碍权限</string>
    <string name="start_automation">开始自动化操作</string>
    <style name="BottomSheetDialogTheme" parent="Theme.Material3.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="CustomBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style>
    <style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style>
    <style name="PermissionBottomSheetDialogTheme" parent="Theme.Material3.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/PermissionBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="PermissionBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style>
    <style name="Theme.Seeq" parent="Theme.Material3.Light">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
</resources>