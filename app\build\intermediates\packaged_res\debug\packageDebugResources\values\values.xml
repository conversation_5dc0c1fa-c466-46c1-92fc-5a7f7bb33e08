<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="bottom_sheet_background">#FFFFFF</color>
    <color name="bottom_sheet_divider">#F0F0F0</color>
    <color name="bottom_sheet_handle">#E0E0E0</color>
    <color name="error">#BA1A1A</color>
    <color name="error_container">#FFDAD6</color>
    <color name="on_error">#FFFFFF</color>
    <color name="on_error_container">#410002</color>
    <color name="on_primary">#FFFFFF</color>
    <color name="on_primary_container">#21005D</color>
    <color name="on_surface">#1C1B1F</color>
    <color name="on_surface_variant">#49454F</color>
    <color name="primary">#6750A4</color>
    <color name="primary_container">#EADDFF</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="surface">#FFFBFE</color>
    <color name="surface_variant">#E7E0EC</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#79747E</color>
    <color name="text_primary">#1C1B1F</color>
    <color name="text_secondary">#49454F</color>
    <color name="white">#FFFFFFFF</color>
    <string name="accessibility_service_description">Seeq自动化操作服务，用于自动操作欢律App</string>
    <string name="app_name">Seeq</string>
    <string name="automation_completed">自动化操作完成</string>
    <string name="automation_failed">自动化操作失败</string>
    <string name="automation_running">自动化操作正在运行...</string>
    <string name="check_permission">检查权限</string>
    <string name="eq_adjusting">正在调节EQ均衡器...</string>
    <string name="eq_completed">EQ调节完成</string>
    <string name="permission_granted">权限已开启</string>
    <string name="permission_required">需要开启无障碍权限</string>
    <string name="start_automation">开始自动化操作</string>
    <style name="ShapeAppearance.Seeq.BottomSheet" parent="ShapeAppearance.Material3.Corner.Large">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearance.Seeq.Button" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>
    <style name="ShapeAppearance.Seeq.TextInputLayout" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>
    <style name="Theme.Seeq" parent="Theme.Material3.DayNight.NoActionBar"/>
    <style name="Theme.Seeq.BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Seeq.BottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="enableEdgeToEdge">true</item>
    </style>
    <style name="Widget.Seeq.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.BottomSheet</item>
        <item name="backgroundTint">@color/bottom_sheet_background</item>
        <item name="android:elevation">24dp</item>
        <item name="behavior_peekHeight">400dp</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">true</item>
        <item name="behavior_expandedOffset">0dp</item>
        <item name="behavior_fitToContents">true</item>
    </style>
    <style name="Widget.Seeq.Button.Primary" parent="Widget.Material3.Button">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.Button</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
    <style name="Widget.Seeq.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.Button</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
    <style name="Widget.Seeq.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.TextInputLayout</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="boxStrokeWidthFocused">3dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>
</resources>