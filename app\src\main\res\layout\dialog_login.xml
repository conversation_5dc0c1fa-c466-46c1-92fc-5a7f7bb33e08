<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/colorSurface"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:backgroundTint="@color/bottom_sheet_handle" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_login"
            android:contentDescription="登录图标"
            app:tint="?attr/colorPrimary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="用户登录"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface" />

    </LinearLayout>

    <!-- 描述文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="需要去QQ群中粘贴并发送指定文本来获取登录码"
        android:textSize="16sp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="32dp" />

    <!-- 登录码输入框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:hint="请输入6位登录码"
        app:boxStrokeWidth="2dp"
        app:boxCornerRadiusTopStart="16dp"
        app:boxCornerRadiusTopEnd="16dp"
        app:boxCornerRadiusBottomStart="16dp"
        app:boxCornerRadiusBottomEnd="16dp"
        app:startIconDrawable="@drawable/ic_key"
        app:startIconTint="?attr/colorPrimary"
        app:helperText="从QQ群机器人获取的6位数字码"
        app:helperTextTextColor="?attr/colorOnSurfaceVariant">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_login_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLength="6"
            android:textSize="18sp"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:textStyle="bold" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="8dp">

        <!-- 复制指令并跳转QQ群按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_copy_and_jump"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            android:text="复制指令并跳转去Q群中发送"
            android:textSize="16sp"
            android:textAllCaps="false"
            app:icon="@drawable/ic_copy_link"
            app:iconGravity="textStart"
            app:iconPadding="8dp"
            app:cornerRadius="16dp"
            style="@style/Widget.Material3.Button.TonalButton" />

        <!-- 登录按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="登录"
            android:textSize="16sp"
            android:textAllCaps="false"
            app:icon="@drawable/ic_login_arrow"
            app:iconGravity="textStart"
            app:iconPadding="8dp"
            app:cornerRadius="16dp"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout>
