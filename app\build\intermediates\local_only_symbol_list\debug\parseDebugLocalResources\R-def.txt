R_DEF: Internal format may change without notice
local
color black
color bottom_sheet_background
color bottom_sheet_background_night
color bottom_sheet_divider
color bottom_sheet_handle
color error
color error_container
color on_error
color on_error_container
color on_primary
color on_primary_container
color on_surface
color on_surface_variant
color primary
color primary_container
color purple_200
color purple_500
color purple_700
color surface
color surface_variant
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color white
drawable bottom_sheet_handle
drawable ic_accessibility
drawable ic_check
drawable ic_copy
drawable ic_key
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_login
drawable ic_settings
drawable ic_warning
drawable warning_background
id btn_already_set
id btn_copy_and_jump
id btn_go_settings
id btn_login
id et_login_code
layout bottom_sheet_login
layout bottom_sheet_permission
layout dialog_login
mipmap ic_launcher
mipmap ic_launcher_round
string accessibility_service_description
string app_name
string automation_completed
string automation_failed
string automation_running
string check_permission
string eq_adjusting
string eq_completed
string permission_granted
string permission_required
string start_automation
style ShapeAppearance.Seeq.BottomSheet
style ShapeAppearance.Seeq.Button
style ShapeAppearance.Seeq.TextInputLayout
style Theme.Seeq
style Theme.Seeq.BottomSheetDialog
style Widget.Seeq.BottomSheet
style Widget.Seeq.Button.Primary
style Widget.Seeq.Button.Secondary
style Widget.Seeq.TextInputLayout
xml accessibility_service_config
xml backup_rules
xml data_extraction_rules
xml file_paths
