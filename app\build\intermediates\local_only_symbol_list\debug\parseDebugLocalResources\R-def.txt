R_DEF: Internal format may change without notice
local
color black
color bottom_sheet_handle
color md_theme_dark_background
color md_theme_dark_error
color md_theme_dark_errorContainer
color md_theme_dark_inverseOnSurface
color md_theme_dark_inversePrimary
color md_theme_dark_inverseSurface
color md_theme_dark_onBackground
color md_theme_dark_onError
color md_theme_dark_onErrorContainer
color md_theme_dark_onPrimary
color md_theme_dark_onPrimaryContainer
color md_theme_dark_onSecondary
color md_theme_dark_onSecondaryContainer
color md_theme_dark_onSurface
color md_theme_dark_onSurfaceVariant
color md_theme_dark_onTertiary
color md_theme_dark_onTertiaryContainer
color md_theme_dark_outline
color md_theme_dark_outlineVariant
color md_theme_dark_primary
color md_theme_dark_primaryContainer
color md_theme_dark_scrim
color md_theme_dark_secondary
color md_theme_dark_secondaryContainer
color md_theme_dark_shadow
color md_theme_dark_surface
color md_theme_dark_surfaceTint
color md_theme_dark_surfaceVariant
color md_theme_dark_tertiary
color md_theme_dark_tertiaryContainer
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_inverseOnSurface
color md_theme_light_inversePrimary
color md_theme_light_inverseSurface
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_scrim
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_shadow
color md_theme_light_surface
color md_theme_light_surfaceTint
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color purple_200
color purple_500
color purple_700
color success_green
color teal_200
color teal_700
color warning_orange
color white
drawable bottom_sheet_handle
drawable ic_accessibility
drawable ic_check
drawable ic_copy_link
drawable ic_info
drawable ic_key
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_login
drawable ic_login_arrow
drawable ic_settings
drawable ic_warning
drawable warning_background
id btn_already_set
id btn_copy_and_jump
id btn_go_settings
id btn_login
id et_login_code
layout dialog_login
layout dialog_permission
mipmap ic_launcher
mipmap ic_launcher_round
string accessibility_service_description
string app_name
string automation_completed
string automation_failed
string automation_running
string check_permission
string eq_adjusting
string eq_completed
string permission_granted
string permission_required
string start_automation
style BottomSheetDialogTheme
style CustomBottomSheetStyle
style CustomShapeAppearanceBottomSheetDialog
style PermissionBottomSheetDialogTheme
style PermissionBottomSheetStyle
style Theme.Seeq
xml accessibility_service_config
xml backup_rules
xml data_extraction_rules
xml file_paths
