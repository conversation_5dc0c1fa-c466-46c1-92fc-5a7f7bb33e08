{"logs": [{"outputFile": "cn.ykload.seeq.app-mergeDebugResources-59:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,425,526,636,724,831,945,1027,1105,1196,1289,1383,1482,1582,1675,1770,1864,1955,2047,2132,2237,2343,2443,2552,2657,2759,2917,16706", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "420,521,631,719,826,940,1022,1100,1191,1284,1378,1477,1577,1670,1765,1859,1950,2042,2127,2232,2338,2438,2547,2652,2754,2912,3018,16785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae282b3edfeee1699c61b5e94e80ed2\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3440,3541,3643,3746,3850,3951,4056,17321", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3536,3638,3741,3845,3946,4051,4162,17417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\282188e067ace35e7ceeb03ac6735130\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,1006,1095,1167,1241,1317,1390,1471,1537", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,1001,1090,1162,1236,1312,1385,1466,1532,1649"}, "to": {"startLines": "48,49,50,51,52,57,58,175,176,178,179,183,185,186,187,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4459,4559,4647,4744,4845,5223,5304,16282,16374,16536,16617,16952,17098,17172,17248,17422,17503,17569", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "4554,4642,4739,4840,4931,5299,5387,16369,16451,16612,16701,17019,17167,17243,17316,17498,17564,17681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ccf8b2c27cbeb3cc7666caa413587752\\transformed\\material-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1035,1099,1193,1261,1320,1415,1478,1542,1601,1668,1731,1785,1900,1958,2020,2074,2145,2277,2361,2440,2532,2616,2696,2830,2906,2982,3111,3195,3274,3331,3382,3448,3518,3596,3667,3747,3817,3893,3971,4042,4140,4226,4309,4402,4495,4568,4640,4734,4788,4872,4939,5023,5111,5175,5240,5304,5374,5476,5580,5676,5777,5838,5893,5973,6060,6135", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,84,79,84,78,95,115,79,60,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,78,91,83,79,133,75,75,128,83,78,56,50,65,69,77,70,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79,86,74,73", "endOffsets": "260,348,433,513,598,677,773,889,969,1030,1094,1188,1256,1315,1410,1473,1537,1596,1663,1726,1780,1895,1953,2015,2069,2140,2272,2356,2435,2527,2611,2691,2825,2901,2977,3106,3190,3269,3326,3377,3443,3513,3591,3662,3742,3812,3888,3966,4037,4135,4221,4304,4397,4490,4563,4635,4729,4783,4867,4934,5018,5106,5170,5235,5299,5369,5471,5575,5671,5772,5833,5888,5968,6055,6130,6204"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,56,59,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177,181,182,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3111,3196,3276,3361,4167,4263,4379,4936,4997,5061,5155,5392,11709,11804,11867,11931,11990,12057,12120,12174,12289,12347,12409,12463,12534,12666,12750,12829,12921,13005,13085,13219,13295,13371,13500,13584,13663,13720,13771,13837,13907,13985,14056,14136,14206,14282,14360,14431,14529,14615,14698,14791,14884,14957,15029,15123,15177,15261,15328,15412,15500,15564,15629,15693,15763,15865,15969,16065,16166,16227,16456,16790,16877,17024", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,56,59,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177,181,182,184", "endColumns": "12,87,84,79,84,78,95,115,79,60,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,78,91,83,79,133,75,75,128,83,78,56,50,65,69,77,70,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79,86,74,73", "endOffsets": "310,3106,3191,3271,3356,3435,4258,4374,4454,4992,5056,5150,5218,5446,11799,11862,11926,11985,12052,12115,12169,12284,12342,12404,12458,12529,12661,12745,12824,12916,13000,13080,13214,13290,13366,13495,13579,13658,13715,13766,13832,13902,13980,14051,14131,14201,14277,14355,14426,14524,14610,14693,14786,14879,14952,15024,15118,15172,15256,15323,15407,15495,15559,15624,15688,15758,15860,15964,16060,16161,16222,16277,16531,16872,16947,17093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,305,418,543,645,747,866,1002,1123,1269,1354,1455,1546,1644,1756,1878,1984,2123,2260,2390,2549,2674,2789,2907,3023,3115,3214,3331,3463,3568,3673,3779,3917,4060,4170,4271,4347,4450,4550,4673,4761,4850,4955,5035,5119,5219,5319,5416,5514,5602,5706,5806,5908,6026,6106,6215", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "178,300,413,538,640,742,861,997,1118,1264,1349,1450,1541,1639,1751,1873,1979,2118,2255,2385,2544,2669,2784,2902,3018,3110,3209,3326,3458,3563,3668,3774,3912,4055,4165,4266,4342,4445,4545,4668,4756,4845,4950,5030,5114,5214,5314,5411,5509,5597,5701,5801,5903,6021,6101,6210,6308"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5451,5579,5701,5814,5939,6041,6143,6262,6398,6519,6665,6750,6851,6942,7040,7152,7274,7380,7519,7656,7786,7945,8070,8185,8303,8419,8511,8610,8727,8859,8964,9069,9175,9313,9456,9566,9667,9743,9846,9946,10069,10157,10246,10351,10431,10515,10615,10715,10812,10910,10998,11102,11202,11304,11422,11502,11611", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "5574,5696,5809,5934,6036,6138,6257,6393,6514,6660,6745,6846,6937,7035,7147,7269,7375,7514,7651,7781,7940,8065,8180,8298,8414,8506,8605,8722,8854,8959,9064,9170,9308,9451,9561,9662,9738,9841,9941,10064,10152,10241,10346,10426,10510,10610,10710,10807,10905,10993,11097,11197,11299,11417,11497,11606,11704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74b732bc14271422d5c17044383b41bc\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,93", "endOffsets": "139,233"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "17686,17775", "endColumns": "88,93", "endOffsets": "17770,17864"}}]}]}