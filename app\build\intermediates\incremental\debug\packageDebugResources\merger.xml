<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\main\res"><file name="ic_launcher_background" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Projects\Seeq\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_outlineVariant">#CAC4D0</color><color name="md_theme_light_surfaceVariant">#E7E0EC</color><color name="md_theme_light_primaryContainer">#EADDFF</color><color name="md_theme_light_onTertiaryContainer">#31111D</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_primary">#6750A4</color><color name="bottom_sheet_handle">#CAC4D0</color><color name="md_theme_light_onBackground">#1C1B1F</color><color name="md_theme_light_tertiary">#7D5260</color><color name="md_theme_light_tertiaryContainer">#FFD8E4</color><color name="md_theme_light_inverseSurface">#313033</color><color name="md_theme_light_background">#FFFBFE</color><color name="md_theme_light_surfaceTint">#6750A4</color><color name="md_theme_light_onPrimaryContainer">#21005D</color><color name="md_theme_light_outline">#79747E</color><color name="md_theme_light_shadow">#000000</color><color name="md_theme_light_onSurfaceVariant">#49454F</color><color name="md_theme_light_scrim">#000000</color><color name="md_theme_light_onSurface">#1C1B1F</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_surface">#FFFBFE</color><color name="success_green">#4CAF50</color><color name="md_theme_light_onSecondaryContainer">#1D192B</color><color name="md_theme_light_inversePrimary">#D0BCFF</color><color name="warning_orange">#FF9800</color><color name="md_theme_light_secondaryContainer">#E8DEF8</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_secondary">#625B71</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_inverseOnSurface">#F4EFF4</color></file><file path="D:\Projects\Seeq\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Seeq</string><string name="accessibility_service_description">Seeq自动化操作服务，用于自动操作欢律App</string><string name="start_automation">开始自动化操作</string><string name="check_permission">检查权限</string><string name="permission_required">需要开启无障碍权限</string><string name="permission_granted">权限已开启</string><string name="automation_running">自动化操作正在运行...</string><string name="automation_completed">自动化操作完成</string><string name="automation_failed">自动化操作失败</string><string name="eq_adjusting">正在调节EQ均衡器...</string><string name="eq_completed">EQ调节完成</string></file><file path="D:\Projects\Seeq\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Seeq" parent="Theme.Material3.DynamicColors.Light">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style><style name="CustomBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style><style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style><style name="PermissionBottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/PermissionBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="BottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="PermissionBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style></file><file name="accessibility_service_config" path="D:\Projects\Seeq\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="D:\Projects\Seeq\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Projects\Seeq\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\Projects\Seeq\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="dialog_login" path="D:\Projects\Seeq\app\src\main\res\layout\dialog_login.xml" qualifiers="" type="layout"/><file name="bottom_sheet_handle" path="D:\Projects\Seeq\app\src\main\res\drawable\bottom_sheet_handle.xml" qualifiers="" type="drawable"/><file name="ic_accessibility" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_accessibility.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_copy_link" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_copy_link.xml" qualifiers="" type="drawable"/><file name="ic_info" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_key" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_key.xml" qualifiers="" type="drawable"/><file name="ic_login" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_login.xml" qualifiers="" type="drawable"/><file name="ic_login_arrow" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_login_arrow.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_warning" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_warning.xml" qualifiers="" type="drawable"/><file name="warning_background" path="D:\Projects\Seeq\app\src\main\res\drawable\warning_background.xml" qualifiers="" type="drawable"/><file name="dialog_permission" path="D:\Projects\Seeq\app\src\main\res\layout\dialog_permission.xml" qualifiers="" type="layout"/><file path="D:\Projects\Seeq\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="md_theme_dark_primary">#D0BCFF</color><color name="md_theme_dark_onPrimary">#381E72</color><color name="md_theme_dark_primaryContainer">#4F378B</color><color name="md_theme_dark_onPrimaryContainer">#EADDFF</color><color name="md_theme_dark_secondary">#CCC2DC</color><color name="md_theme_dark_onSecondary">#332D41</color><color name="md_theme_dark_secondaryContainer">#4A4458</color><color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color><color name="md_theme_dark_tertiary">#EFB8C8</color><color name="md_theme_dark_onTertiary">#492532</color><color name="md_theme_dark_tertiaryContainer">#633B48</color><color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color><color name="md_theme_dark_error">#F2B8B5</color><color name="md_theme_dark_errorContainer">#8C1D18</color><color name="md_theme_dark_onError">#601410</color><color name="md_theme_dark_onErrorContainer">#F9DEDC</color><color name="md_theme_dark_background">#10131C</color><color name="md_theme_dark_onBackground">#E6E0E9</color><color name="md_theme_dark_surface">#10131C</color><color name="md_theme_dark_onSurface">#E6E0E9</color><color name="md_theme_dark_surfaceVariant">#49454F</color><color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color><color name="md_theme_dark_outline">#938F99</color><color name="md_theme_dark_inverseOnSurface">#10131C</color><color name="md_theme_dark_inverseSurface">#E6E0E9</color><color name="md_theme_dark_inversePrimary">#6750A4</color><color name="md_theme_dark_shadow">#000000</color><color name="md_theme_dark_surfaceTint">#D0BCFF</color><color name="md_theme_dark_outlineVariant">#49454F</color><color name="md_theme_dark_scrim">#000000</color><color name="bottom_sheet_handle">#938F99</color><color name="success_green">#81C784</color><color name="warning_orange">#FFB74D</color></file><file path="D:\Projects\Seeq\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Seeq" parent="Theme.Material3.DynamicColors.Dark">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style><style name="BottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Dark.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="CustomBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style><style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style><style name="PermissionBottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Dark.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/PermissionBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="PermissionBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>