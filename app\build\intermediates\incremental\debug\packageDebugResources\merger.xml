<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\main\res"><file name="ic_launcher_background" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projects\Seeq\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Projects\Seeq\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="bottom_sheet_handle">#E0E0E0</color><color name="bottom_sheet_background">#FFFFFF</color><color name="surface">#FFFBFE</color><color name="on_surface_variant">#49454F</color><color name="primary">#6750A4</color><color name="on_surface">#1C1B1F</color><color name="text_hint">#79747E</color><color name="primary_container">#EADDFF</color><color name="on_primary_container">#21005D</color><color name="surface_variant">#E7E0EC</color><color name="text_secondary">#49454F</color><color name="error_container">#FFDAD6</color><color name="on_error">#FFFFFF</color><color name="bottom_sheet_divider">#F0F0F0</color><color name="error">#BA1A1A</color><color name="on_error_container">#410002</color><color name="text_primary">#1C1B1F</color><color name="on_primary">#FFFFFF</color></file><file path="D:\Projects\Seeq\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Seeq</string><string name="accessibility_service_description">Seeq自动化操作服务，用于自动操作欢律App</string><string name="start_automation">开始自动化操作</string><string name="check_permission">检查权限</string><string name="permission_required">需要开启无障碍权限</string><string name="permission_granted">权限已开启</string><string name="automation_running">自动化操作正在运行...</string><string name="automation_completed">自动化操作完成</string><string name="automation_failed">自动化操作失败</string><string name="eq_adjusting">正在调节EQ均衡器...</string><string name="eq_completed">EQ调节完成</string></file><file path="D:\Projects\Seeq\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Seeq" parent="Theme.Material3.DayNight.NoActionBar"/><style name="Widget.Seeq.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.Button</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style><style name="Widget.Seeq.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.TextInputLayout</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="boxStrokeWidthFocused">3dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style><style name="ShapeAppearance.Seeq.BottomSheet" parent="ShapeAppearance.Material3.Corner.Large">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style><style name="Theme.Seeq.BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Seeq.BottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="enableEdgeToEdge">true</item>
    </style><style name="Widget.Seeq.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.BottomSheet</item>
        <item name="backgroundTint">@color/bottom_sheet_background</item>
        <item name="android:elevation">24dp</item>
        <item name="behavior_peekHeight">0dp</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">false</item>
    </style><style name="Widget.Seeq.Button.Primary" parent="Widget.Material3.Button">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.Button</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style><style name="ShapeAppearance.Seeq.TextInputLayout" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style><style name="ShapeAppearance.Seeq.Button" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style></file><file name="accessibility_service_config" path="D:\Projects\Seeq\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="D:\Projects\Seeq\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Projects\Seeq\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\Projects\Seeq\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="dialog_login" path="D:\Projects\Seeq\app\src\main\res\layout\dialog_login.xml" qualifiers="" type="layout"/><file name="bottom_sheet_handle" path="D:\Projects\Seeq\app\src\main\res\drawable\bottom_sheet_handle.xml" qualifiers="" type="drawable"/><file name="ic_accessibility" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_accessibility.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_key" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_key.xml" qualifiers="" type="drawable"/><file name="ic_login" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_login.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_warning" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_warning.xml" qualifiers="" type="drawable"/><file name="warning_background" path="D:\Projects\Seeq\app\src\main\res\drawable\warning_background.xml" qualifiers="" type="drawable"/><file name="ic_copy" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_copy.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_login" path="D:\Projects\Seeq\app\src\main\res\layout\bottom_sheet_login.xml" qualifiers="" type="layout"/><file name="bottom_sheet_permission" path="D:\Projects\Seeq\app\src\main\res\layout\bottom_sheet_permission.xml" qualifiers="" type="layout"/><file path="D:\Projects\Seeq\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="bottom_sheet_background_night">#1E1E1E</color><color name="bottom_sheet_handle">#616161</color><color name="bottom_sheet_divider">#2E2E2E</color><color name="text_primary">#E6E1E5</color><color name="text_secondary">#CAC4D0</color><color name="text_hint">#938F99</color><color name="primary">#D0BCFF</color><color name="primary_container">#4F378B</color><color name="on_primary">#371E73</color><color name="on_primary_container">#EADDFF</color><color name="surface">#141218</color><color name="surface_variant">#49454F</color><color name="on_surface">#E6E1E5</color><color name="on_surface_variant">#CAC4D0</color><color name="error">#FFB4AB</color><color name="error_container">#93000A</color><color name="on_error">#690005</color><color name="on_error_container">#FFDAD6</color></file><file path="D:\Projects\Seeq\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Seeq" parent="Theme.Material3.DayNight.NoActionBar"/><style name="Theme.Seeq.BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Seeq.BottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="Widget.Seeq.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.BottomSheet</item>
        <item name="backgroundTint">@color/bottom_sheet_background_night</item>
        <item name="android:elevation">16dp</item>
    </style></file><file name="bottom_sheet_background" path="D:\Projects\Seeq\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="ic_close" path="D:\Projects\Seeq\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_background" path="D:\Projects\Seeq\app\src\main\res\drawable-night\bottom_sheet_background.xml" qualifiers="night-v8" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\Seeq\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>