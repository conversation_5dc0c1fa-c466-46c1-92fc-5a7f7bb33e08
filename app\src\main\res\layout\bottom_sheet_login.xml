<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/bottom_sheet_background"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽手柄 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:alpha="0.4" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_login"
            android:tint="@color/primary"
            android:contentDescription="登录图标" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="用户登录"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 描述文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="当前未登录，需要去QQ群中粘贴并发送指定文本来获取登录码"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:lineSpacingExtra="4dp" />

    <!-- 登录码输入区域 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:hint="请输入6位登录码"
        style="@style/Widget.Seeq.TextInputLayout"
        app:startIconDrawable="@drawable/ic_key"
        app:startIconTint="@color/primary"
        app:helperText="登录码为6位纯数字"
        app:helperTextTextColor="@color/text_hint"
        app:counterEnabled="true"
        app:counterMaxLength="6">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_login_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLength="6"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:textColor="@color/text_primary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 复制指令并跳转QQ群按钮 -->
        <Button
            android:id="@+id/btn_copy_and_jump"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="复制指令并跳转去Q群中发送"
            android:textSize="16sp"
            android:textStyle="bold"
            style="@style/Widget.Seeq.Button.Secondary"
            app:icon="@drawable/ic_copy"
            app:iconGravity="start"
            app:iconTint="@color/primary" />

        <!-- 登录按钮 -->
        <Button
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="登录"
            android:textSize="16sp"
            android:textStyle="bold"
            style="@style/Widget.Seeq.Button.Primary"
            app:icon="@drawable/ic_login"
            app:iconGravity="start" />

    </LinearLayout>

    <!-- 底部提示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="登录后可享受个性化EQ推荐服务"
        android:textSize="14sp"
        android:textColor="@color/text_hint"
        android:gravity="center"
        android:alpha="0.8" />

</LinearLayout>
