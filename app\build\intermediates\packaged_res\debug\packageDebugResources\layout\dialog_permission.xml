<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/colorSurface"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:backgroundTint="@color/bottom_sheet_handle" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_accessibility"
            android:contentDescription="无障碍图标"
            app:tint="?attr/colorPrimary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="需要无障碍权限"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface" />

    </LinearLayout>

    <!-- 描述文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="需要无障碍权限以自动导入EQ设置"
        android:textSize="16sp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="16dp" />

    <!-- 功能说明卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:strokeWidth="1dp"
        app:strokeColor="?attr/colorOutlineVariant"
        app:cardBackgroundColor="?attr/colorSurfaceVariant">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_info"
                    app:tint="?attr/colorPrimary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="权限用途说明"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="• 自动打开音频设置应用\n• 自动调整EQ均衡器参数\n• 提升您的音频体验"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 提示信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp"
        android:padding="12dp"
        android:background="@drawable/warning_background">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_warning"
            app:tint="@color/warning_orange" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="若Seeq后台被杀，则需要重新设置权限"
            android:textSize="13sp"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 去设置按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_go_settings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            android:text="去设置"
            android:textSize="16sp"
            android:textAllCaps="false"
            app:icon="@drawable/ic_settings"
            app:iconGravity="textStart"
            app:iconPadding="8dp"
            app:cornerRadius="16dp"
            style="@style/Widget.Material3.Button" />

        <!-- 我已设置按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_already_set"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="我已设置"
            android:textSize="16sp"
            android:textAllCaps="false"
            app:icon="@drawable/ic_check"
            app:iconGravity="textStart"
            app:iconPadding="8dp"
            app:cornerRadius="16dp"
            style="@style/Widget.Material3.Button.TonalButton" />

    </LinearLayout>

</LinearLayout>
