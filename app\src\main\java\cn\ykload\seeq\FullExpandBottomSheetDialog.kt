package cn.ykload.seeq

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

/**
 * 自定义底部模态框，确保完全展开显示
 */
class FullExpandBottomSheetDialog(context: Context, theme: Int) : BottomSheetDialog(context, theme) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置窗口属性
        window?.let { window ->
            window.setDimAmount(0.5f)
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT
        }
    }

    override fun setContentView(view: View) {
        super.setContentView(view)
        setupFullExpand()
    }

    override fun setContentView(layoutResId: Int) {
        super.setContentView(layoutResId)
        setupFullExpand()
    }

    override fun setContentView(view: View, params: ViewGroup.LayoutParams?) {
        super.setContentView(view, params)
        setupFullExpand()
    }

    private fun setupFullExpand() {
        // 延迟设置，确保视图已经创建
        findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)?.let { bottomSheet ->
            val behavior = BottomSheetBehavior.from(bottomSheet)
            
            // 设置行为属性
            behavior.skipCollapsed = true
            behavior.isHideable = true
            behavior.isDraggable = true
            
            // 设置完全展开
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
            
            // 设置peek高度为屏幕高度的80%
            val displayMetrics = context.resources.displayMetrics
            behavior.peekHeight = (displayMetrics.heightPixels * 0.8).toInt()
            
            // 监听状态变化，防止折叠
            behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == BottomSheetBehavior.STATE_COLLAPSED) {
                        behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    }
                }
                
                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    // 可以在这里添加滑动动画效果
                }
            })
        }
    }

    override fun show() {
        super.show()
        
        // 显示后再次确保展开
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            try {
                findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)?.let { bottomSheet ->
                    val behavior = BottomSheetBehavior.from(bottomSheet)
                    behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
            } catch (e: Exception) {
                android.util.Log.w("FullExpandBottomSheet", "无法设置展开状态", e)
            }
        }, 50)
    }
}
