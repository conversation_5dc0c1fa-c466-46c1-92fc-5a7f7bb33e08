package cn.ykload.seeq

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.webkit.*
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.textfield.TextInputEditText
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern

class MainActivity : AppCompatActivity(), AutomationCallback {

    companion object {
        private const val TAG = "MainActivity"
        private const val SEEQ_URL = "https://seeq.ykload.com"
        private val EQ_PATTERN = Pattern.compile("\\[Seeq\\]\\{([^}]+)\\}")

        // 权限请求码
        private const val PERMISSION_REQUEST_CODE = 1001

        // 登录相关常量
        private const val QQ_GROUP_COMMAND = "小真寻，登录码，欧内盖~"
        private const val QQ_GROUP_URL = "mqqapi://card/show_pslcard?src_type=internal&version=1&uin=791424491&card_type=group&source=qrcode"
    }

    private lateinit var webView: WebView

    // 文件上传相关
    private var fileUploadCallback: ValueCallback<Array<Uri>>? = null
    private var cameraPhotoPath: String? = null

    // Activity Result Launchers
    private lateinit var fileChooserLauncher: ActivityResultLauncher<Intent>
    private lateinit var permissionLauncher: ActivityResultLauncher<Array<String>>

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化Activity Result Launchers
        initializeActivityResultLaunchers()

        // 创建WebView
        webView = WebView(this)
        setContentView(webView)

        // 配置WebView
        setupWebView()

        // 设置自动化完成回调
        SeqAccessibilityService.instance?.setAutomationCallback(this)

        // 检查登录状态
        checkLoginStatus()
    }

    /**
     * 初始化Activity Result Launchers
     */
    private fun initializeActivityResultLaunchers() {
        // 文件选择器启动器
        fileChooserLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleFileChooserResult(result.resultCode, result.data)
        }

        // 权限请求启动器
        permissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            handlePermissionResult(permissions)
        }
    }

    /**
     * 检查登录状态
     */
    private fun checkLoginStatus() {
        if (UserManager.isLoggedIn(this)) {
            val userID = UserManager.getUserID(this)
            Log.d(TAG, "用户已登录，用户ID: $userID")
            loadWebViewWithUserID(userID!!)
        } else {
            Log.d(TAG, "用户未登录，显示登录弹窗")
            showLoginDialog()
        }
    }

    /**
     * 使用用户ID加载WebView
     */
    private fun loadWebViewWithUserID(userID: String) {
        // 设置包含用户ID的User-Agent
        val originalUserAgent = webView.settings.userAgentString
        val customUserAgent = "$originalUserAgent SeeqApp/1.0 UserID/$userID"
        webView.settings.userAgentString = customUserAgent

        Log.d(TAG, "设置User-Agent: $customUserAgent")

        // 加载网页
        webView.loadUrl(SEEQ_URL)
        Log.d(TAG, "WebView已开始加载: $SEEQ_URL")
    }

    /**
     * 配置WebView设置
     */
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        val webSettings = webView.settings

        // 启用JavaScript
        webSettings.javaScriptEnabled = true

        // 启用DOM存储
        webSettings.domStorageEnabled = true

        // 启用数据库存储
        webSettings.databaseEnabled = true


        // 设置缓存模式
        webSettings.cacheMode = WebSettings.LOAD_DEFAULT

        // 允许文件访问
        webSettings.allowFileAccess = true

        // 允许内容访问
        webSettings.allowContentAccess = true

        // 支持缩放
        webSettings.setSupportZoom(true)
        webSettings.builtInZoomControls = true
        webSettings.displayZoomControls = false

        // 设置用户代理
        webSettings.userAgentString = webSettings.userAgentString + " SeeqApp/1.0"

        // 设置WebViewClient
        webView.webViewClient = SeeqWebViewClient()

        // 设置WebChromeClient用于监听控制台消息
        webView.webChromeClient = SeeqWebChromeClient()

        Log.d(TAG, "WebView配置完成")
    }

    /**
     * 自定义WebViewClient
     */
    private inner class SeeqWebViewClient : WebViewClient() {

        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            // 允许在当前WebView中加载所有URL
            return false
        }

        override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
            super.onPageStarted(view, url, favicon)
            Log.d(TAG, "开始加载页面: $url")
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            Log.d(TAG, "页面加载完成: $url")

            // 注入JavaScript代码来监听控制台输出
            injectConsoleMonitor()
        }

        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
            super.onReceivedError(view, request, error)
            Log.e(TAG, "WebView加载错误: ${error?.description}")
        }
    }

    /**
     * 自定义WebChromeClient用于监听控制台消息和处理文件上传
     */
    private inner class SeeqWebChromeClient : WebChromeClient() {

        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
            consoleMessage?.let { message ->
                val logMessage = "[WebView Console] ${message.message()}"
                Log.d(TAG, logMessage)

                // 检查是否包含Seeq EQ参数
                checkForEQParameters(message.message())
            }
            return true
        }

        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            if (newProgress == 100) {
                Log.d(TAG, "页面加载进度: 100%")
            }
        }

        // 处理文件上传（Android 5.0+）
        override fun onShowFileChooser(
            webView: WebView?,
            filePathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            Log.d(TAG, "文件选择器被触发")

            // 保存回调
            fileUploadCallback = filePathCallback

            // 检查权限并启动文件选择
            if (checkAndRequestPermissions()) {
                showFileChooser(fileChooserParams)
            }

            return true
        }
    }

    /**
     * 注入JavaScript代码来监听控制台输出
     */
    private fun injectConsoleMonitor() {
        val jsCode = """
            (function() {
                // 保存原始的console.log方法
                var originalLog = console.log;
                var originalWarn = console.warn;
                var originalError = console.error;
                var originalInfo = console.info;

                // 重写console.log方法
                console.log = function() {
                    originalLog.apply(console, arguments);
                    // 通过Android接口发送消息
                    if (window.Android) {
                        window.Android.onConsoleMessage('log', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.warn = function() {
                    originalWarn.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('warn', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.error = function() {
                    originalError.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('error', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.info = function() {
                    originalInfo.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('info', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.log('Seeq控制台监听已启动');
            })();
        """.trimIndent()

        webView.evaluateJavascript(jsCode) { result ->
            Log.d(TAG, "控制台监听脚本注入完成: $result")
        }

        // 添加JavaScript接口
        webView.addJavascriptInterface(JavaScriptInterface(), "Android")
    }

    /**
     * JavaScript接口类
     */
    private inner class JavaScriptInterface {

        @JavascriptInterface
        fun onConsoleMessage(level: String, message: String) {
            Log.d(TAG, "[$level] $message")

            // 在主线程中处理控制台消息
            runOnUiThread {
                checkForEQParameters(message)
            }
        }
    }

    /**
     * 检查控制台消息中是否包含EQ参数
     */
    private fun checkForEQParameters(message: String) {
        val matcher = EQ_PATTERN.matcher(message)
        if (matcher.find()) {
            val eqParams = matcher.group(1)
            Log.d(TAG, "检测到EQ参数: $eqParams")

            // 解析EQ参数
            val params = parseEQParameters(eqParams)
            if (params != null) {
                Log.d(TAG, "解析的EQ参数: ${params.contentToString()}")

                // 触发自动化操作
                triggerAutomation(params)
            } else {
                Log.w(TAG, "EQ参数解析失败: $eqParams")
            }
        }
    }

    /**
     * 解析EQ参数字符串
     * 格式: +1,+1,+4,+5,+1,+4
     */
    private fun parseEQParameters(paramString: String): Array<Int>? {
        return try {
            val params = paramString.split(",")
            if (params.size != 6) {
                Log.w(TAG, "EQ参数数量不正确，期望6个，实际${params.size}个")
                return null
            }

            val result = Array(6) { 0 }
            for (i in params.indices) {
                val param = params[i].trim()
                result[i] = when {
                    param.startsWith("+") -> param.substring(1).toInt()
                    else -> param.toInt()
                }

                // 验证参数范围 (-6 到 +6)
                if (result[i] < -6 || result[i] > 6) {
                    Log.w(TAG, "EQ参数超出范围: ${result[i]}")
                    return null
                }
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "解析EQ参数时出错: $paramString", e)
            null
        }
    }

    /**
     * 触发自动化操作
     */
    private fun triggerAutomation(eqParams: Array<Int>) {
        Log.d(TAG, "准备触发自动化操作，EQ参数: ${eqParams.contentToString()}")

        // 检查无障碍权限
        if (!AccessibilityPermissionHelper.isAccessibilityServiceEnabled(this)) {
            Log.w(TAG, "无障碍权限未授予，显示权限请求弹窗")
            showPermissionDialog(eqParams)
            return
        }

        // 检查设备是否支持EQ自动化
        if (!AccessibilityPermissionHelper.isEQAutomationSupported(this)) {
            Log.w(TAG, "设备不支持EQ自动化")
            val deviceInfo = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
            Toast.makeText(this, "当前设备($deviceInfo)不支持EQ自动化功能", Toast.LENGTH_LONG).show()
            return
        }

        // 显示使用的自动化方式
        val hasHeytapApp = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(this)
        val automationMethod = if (hasHeytapApp) {
            "使用欢律App进行自动化"
        } else {
            "使用系统蓝牙设置进行自动化（OPPO系列设备）"
        }
        Log.d(TAG, automationMethod)

        // 设置EQ参数并启动自动化
        SeqAccessibilityService.instance?.setCustomEQGains(eqParams)
        SeqAccessibilityService.instance?.startAutomation()

        Toast.makeText(this, "开始自动导入EQ设置", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "自动化操作已启动")
    }

    /**
     * 显示权限请求底部模态框
     */
    private fun showPermissionDialog(eqParams: Array<Int>) {
        // 创建底部模态框
        val bottomSheetDialog = BottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_permission, null)

        // 获取控件引用
        val goSettingsButton = dialogView.findViewById<Button>(R.id.btn_go_settings)
        val alreadySetButton = dialogView.findViewById<Button>(R.id.btn_already_set)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true)  // 允许通过手势关闭权限弹窗

        // 关闭按钮点击
        closeButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            bottomSheetDialog.dismiss()
        }

        // 去设置按钮点击
        goSettingsButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            AccessibilityPermissionHelper.openAccessibilitySettings(this)
        }

        // 我已设置按钮点击
        alreadySetButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 重新检查权限
            if (AccessibilityPermissionHelper.isAccessibilityServiceEnabled(this)) {
                // 权限已授予，关闭弹窗并启动自动化
                bottomSheetDialog.dismiss()

                // 显示成功提示
                Toast.makeText(this, "权限验证成功，开始自动化操作", Toast.LENGTH_SHORT).show()

                // 启动自动化
                triggerAutomation(eqParams)
            } else {
                // 权限仍未授予，显示提示但不关闭弹窗
                Toast.makeText(this, "权限尚未授予，请先去设置中开启", Toast.LENGTH_LONG).show()

                // 添加震动提示效果
                alreadySetButton.animate()
                    .translationX(-10f)
                    .setDuration(50)
                    .withEndAction {
                        alreadySetButton.animate()
                            .translationX(10f)
                            .setDuration(50)
                            .withEndAction {
                                alreadySetButton.animate()
                                    .translationX(0f)
                                    .setDuration(50)
                                    .start()
                            }
                            .start()
                    }
                    .start()
            }
        }

        // 显示底部模态框
        bottomSheetDialog.show()

        // 确保模态框完全展开
        bottomSheetDialog.setOnShowListener {
            val bottomSheet = bottomSheetDialog.findViewById<android.widget.FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                val behavior = BottomSheetBehavior.from(sheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.skipCollapsed = true
            }
        }
    }

    @SuppressLint("GestureBackNavigation")
    override fun onResume() {
        super.onResume()
        // 确保回调设置正确（防止服务重启后回调丢失）
        SeqAccessibilityService.instance?.setAutomationCallback(this)
    }

    @SuppressLint("GestureBackNavigation")
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        // 清除回调
        SeqAccessibilityService.instance?.setAutomationCallback(null)
        webView.destroy()
        super.onDestroy()
    }

    /**
     * 自动化完成回调实现
     */
    override fun onAutomationCompleted(eqParams: Array<Int>) {
        runOnUiThread {
            showSuccessMessages(eqParams)
        }
    }

    /**
     * 显示成功消息气泡
     */
    private fun showSuccessMessages(eqParams: Array<Int>) {
        // 格式化EQ参数为字符串
        val eqString = eqParams.joinToString(",") { value ->
            if (value > 0) "+$value" else value.toString()
        }

        // 第一个消息：EQ应用成功
        val firstMessage = "EQ应用成功：{$eqString}"
        Toast.makeText(this, firstMessage, Toast.LENGTH_LONG).show()

        Log.d(TAG, "显示第一个成功消息: $firstMessage")

        // 延迟显示第二个消息
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            val secondMessage = "若以上EQ与目前调整EQ的不一致，请切回Seeq再进行一次导入。"
            Toast.makeText(this, secondMessage, Toast.LENGTH_LONG).show()

            Log.d(TAG, "显示第二个成功消息: $secondMessage")
        }, 3500) // 3.5秒后显示第二个消息，确保第一个消息显示完毕
    }

    /**
     * 检查并请求必要的权限
     */
    private fun checkAndRequestPermissions(): Boolean {
        val permissions = mutableListOf<String>()

        // 检查相机权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.CAMERA)
        }

        // 根据Android版本检查存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用新的媒体权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            }
        } else {
            // Android 12 及以下使用传统存储权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }

        return if (permissions.isNotEmpty()) {
            Log.d(TAG, "请求权限: ${permissions.joinToString()}")
            permissionLauncher.launch(permissions.toTypedArray())
            false
        } else {
            Log.d(TAG, "所有权限已授予")
            true
        }
    }

    /**
     * 处理权限请求结果
     */
    private fun handlePermissionResult(permissions: Map<String, Boolean>) {
        val allGranted = permissions.values.all { it }

        if (allGranted) {
            Log.d(TAG, "所有权限已授予，启动文件选择器")
            showFileChooser(null)
        } else {
            Log.w(TAG, "权限被拒绝")
            Toast.makeText(this, "需要相机和存储权限才能上传图片", Toast.LENGTH_LONG).show()

            // 取消文件上传
            fileUploadCallback?.onReceiveValue(null)
            fileUploadCallback = null
        }
    }

    /**
     * 显示文件选择器
     */
    private fun showFileChooser(fileChooserParams: WebChromeClient.FileChooserParams?) {
        try {
            val intents = mutableListOf<Intent>()

            // 创建图片选择Intent
            val galleryIntent = Intent(Intent.ACTION_GET_CONTENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "image/*"
                putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            }

            // 创建相机拍照Intent
            val cameraIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            if (cameraIntent.resolveActivity(packageManager) != null) {
                // 创建临时文件用于保存拍照结果
                val photoFile = createImageFile()
                if (photoFile != null) {
                    cameraPhotoPath = photoFile.absolutePath
                    val photoURI = FileProvider.getUriForFile(
                        this,
                        "${packageName}.fileprovider",
                        photoFile
                    )
                    cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                    intents.add(cameraIntent)
                }
            }

            // 创建选择器Intent
            val chooserIntent = Intent.createChooser(galleryIntent, "选择图片")
            if (intents.isNotEmpty()) {
                chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, intents.toTypedArray())
            }

            Log.d(TAG, "启动文件选择器")
            fileChooserLauncher.launch(chooserIntent)

        } catch (e: Exception) {
            Log.e(TAG, "启动文件选择器失败", e)
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show()

            // 取消文件上传
            fileUploadCallback?.onReceiveValue(null)
            fileUploadCallback = null
        }
    }

    /**
     * 创建临时图片文件
     */
    private fun createImageFile(): File? {
        return try {
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFileName = "JPEG_${timeStamp}_"
            val storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            File.createTempFile(imageFileName, ".jpg", storageDir)
        } catch (e: IOException) {
            Log.e(TAG, "创建图片文件失败", e)
            null
        }
    }

    /**
     * 处理文件选择结果
     */
    private fun handleFileChooserResult(resultCode: Int, data: Intent?) {
        val results = mutableListOf<Uri>()

        try {
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    // 处理从图库选择的图片
                    val clipData = data.clipData
                    if (clipData != null) {
                        // 多选图片
                        for (i in 0 until clipData.itemCount) {
                            val uri = clipData.getItemAt(i).uri
                            results.add(uri)
                            Log.d(TAG, "选择的图片: $uri")
                        }
                    } else {
                        // 单选图片
                        data.data?.let { uri ->
                            results.add(uri)
                            Log.d(TAG, "选择的图片: $uri")
                        }
                    }
                } else {
                    // 处理相机拍照结果
                    cameraPhotoPath?.let { path ->
                        val file = File(path)
                        if (file.exists()) {
                            val uri = FileProvider.getUriForFile(
                                this,
                                "${packageName}.fileprovider",
                                file
                            )
                            results.add(uri)
                            Log.d(TAG, "拍照结果: $uri")
                        }
                    }
                }
            }

            Log.d(TAG, "文件选择完成，共选择 ${results.size} 个文件")

        } catch (e: Exception) {
            Log.e(TAG, "处理文件选择结果时出错", e)
        } finally {
            // 将结果传递给WebView
            fileUploadCallback?.onReceiveValue(
                if (results.isNotEmpty()) results.toTypedArray() else null
            )
            fileUploadCallback = null
            cameraPhotoPath = null
        }
    }

    /**
     * 显示登录底部模态框
     */
    private fun showLoginDialog() {
        // 创建底部模态框
        val bottomSheetDialog = BottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_login, null)

        // 获取控件引用
        val loginCodeInput = dialogView.findViewById<TextInputEditText>(R.id.et_login_code)
        val copyAndJumpButton = dialogView.findViewById<Button>(R.id.btn_copy_and_jump)
        val loginButton = dialogView.findViewById<Button>(R.id.btn_login)

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(false)

        // 初始状态：登录按钮不可点击
        loginButton.isEnabled = false
        loginButton.alpha = 0.5f

        // 监听登录码输入
        loginCodeInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val loginCode = s?.toString() ?: ""
                val isValid = loginCode.length == 6 && loginCode.all { it.isDigit() }

                loginButton.isEnabled = isValid
                loginButton.alpha = if (isValid) 1.0f else 0.5f

                // 添加输入框动画效果
                if (isValid) {
                    loginCodeInput.animate()
                        .scaleX(1.02f)
                        .scaleY(1.02f)
                        .setDuration(100)
                        .withEndAction {
                            loginCodeInput.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .setDuration(100)
                                .start()
                        }
                        .start()
                }
            }
        })

        // 复制指令并跳转QQ群
        copyAndJumpButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            copyCommandAndJumpToQQ()
        }

        // 登录按钮点击
        loginButton.setOnClickListener {
            val loginCode = loginCodeInput.text.toString()
            if (loginCode.length == 6 && loginCode.all { it.isDigit() }) {
                // 添加按钮点击动画
                it.animate()
                    .scaleX(0.95f)
                    .scaleY(0.95f)
                    .setDuration(100)
                    .withEndAction {
                        it.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start()
                    }
                    .start()

                performLogin(loginCode, bottomSheetDialog)
            } else {
                Toast.makeText(this, "请输入6位数字登录码", Toast.LENGTH_SHORT).show()
            }
        }

        // 显示底部模态框
        bottomSheetDialog.show()

        // 确保模态框完全展开
        bottomSheetDialog.setOnShowListener {
            val bottomSheet = bottomSheetDialog.findViewById<android.widget.FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                val behavior = BottomSheetBehavior.from(sheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.skipCollapsed = true
            }
        }
    }

    /**
     * 复制指令并跳转QQ群
     */
    private fun copyCommandAndJumpToQQ() {
        try {
            // 复制指令到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("QQ群指令", QQ_GROUP_COMMAND)
            clipboard.setPrimaryClip(clip)

            Toast.makeText(this, "指令已复制到剪贴板", Toast.LENGTH_SHORT).show()

            // 尝试跳转QQ群
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(QQ_GROUP_URL))
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
            } catch (e: Exception) {
                Log.w(TAG, "无法直接跳转QQ群", e)
                Toast.makeText(this, "请手动打开QQ群并发送已复制的指令", Toast.LENGTH_LONG).show()
            }

        } catch (e: Exception) {
            Log.e(TAG, "复制指令失败", e)
            Toast.makeText(this, "复制指令失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 执行登录
     */
    private fun performLogin(loginCode: String, dialog: BottomSheetDialog) {
        Log.d(TAG, "开始执行登录，登录码: $loginCode")

        // 显示加载状态
        val loginButton = dialog.findViewById<Button>(R.id.btn_login)
        loginButton?.let {
            it.isEnabled = false
            it.text = "登录中..."

            // 添加加载动画
            it.animate()
                .alpha(0.7f)
                .setDuration(200)
                .start()
        }

        // 执行登录请求
        LoginService.login(loginCode, object : LoginCallback {
            override fun onSuccess(userID: String) {
                runOnUiThread {
                    Log.d(TAG, "登录成功，用户ID: $userID")

                    // 保存用户ID
                    UserManager.saveUserID(this@MainActivity, userID)

                    // 关闭弹窗
                    dialog.dismiss()

                    // 显示成功提示
                    Toast.makeText(this@MainActivity, "登录成功！", Toast.LENGTH_SHORT).show()

                    // 加载WebView
                    loadWebViewWithUserID(userID)
                }
            }

            override fun onError(message: String) {
                runOnUiThread {
                    Log.w(TAG, "登录失败: $message")

                    // 恢复按钮状态
                    loginButton?.let {
                        it.isEnabled = true
                        it.text = "登录"

                        // 恢复按钮透明度动画
                        it.animate()
                            .alpha(1.0f)
                            .setDuration(200)
                            .start()
                    }

                    // 显示错误提示
                    Toast.makeText(this@MainActivity, "登录失败: $message", Toast.LENGTH_LONG).show()
                }
            }
        })
    }
}

