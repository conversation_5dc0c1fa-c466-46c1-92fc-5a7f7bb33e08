<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Seeq" parent="Theme.Material3.DynamicColors.Dark">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>

    <!-- 底部弹窗样式 -->
    <style name="BottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Dark.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="CustomBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style>

    <style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style>

    <!-- 权限弹窗样式 -->
    <style name="PermissionBottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Dark.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/PermissionBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="PermissionBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style>

</resources>
