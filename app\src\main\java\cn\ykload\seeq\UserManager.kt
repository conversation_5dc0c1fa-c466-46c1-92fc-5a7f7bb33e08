package cn.ykload.seeq

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 用户数据管理类
 */
object UserManager {
    
    private const val TAG = "UserManager"
    private const val PREFS_NAME = "seeq_user_prefs"
    private const val KEY_USER_ID = "user_id"
    
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 保存用户ID
     */
    fun saveUserID(context: Context, userID: String) {
        try {
            getPreferences(context).edit()
                .putString(KEY_USER_ID, userID)
                .apply()
            Log.d(TAG, "用户ID已保存: $userID")
        } catch (e: Exception) {
            Log.e(TAG, "保存用户ID失败", e)
        }
    }
    
    /**
     * 获取用户ID
     */
    fun getUserID(context: Context): String? {
        return try {
            val userID = getPreferences(context).getString(KEY_USER_ID, null)
            Log.d(TAG, "获取用户ID: ${userID ?: "未找到"}")
            userID
        } catch (e: Exception) {
            Log.e(TAG, "获取用户ID失败", e)
            null
        }
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(context: Context): Boolean {
        val userID = getUserID(context)
        val isLoggedIn = !userID.isNullOrEmpty() && userID.length == 16
        Log.d(TAG, "登录状态检查: $isLoggedIn")
        return isLoggedIn
    }
    
    /**
     * 清除用户数据（登出）
     */
    fun clearUserData(context: Context) {
        try {
            getPreferences(context).edit()
                .remove(KEY_USER_ID)
                .apply()
            Log.d(TAG, "用户数据已清除")
        } catch (e: Exception) {
            Log.e(TAG, "清除用户数据失败", e)
        }
    }
    
    /**
     * 验证用户ID格式
     */
    fun isValidUserID(userID: String?): Boolean {
        if (userID.isNullOrEmpty()) return false
        
        // 检查是否为16位数字
        val isValid = userID.length == 16 && userID.all { it.isDigit() }
        Log.d(TAG, "用户ID格式验证: $userID -> $isValid")
        return isValid
    }
}
