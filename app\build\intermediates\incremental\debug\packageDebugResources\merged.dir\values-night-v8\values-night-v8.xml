<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="bottom_sheet_background_night">#1E1E1E</color>
    <color name="bottom_sheet_divider">#2E2E2E</color>
    <color name="bottom_sheet_handle">#616161</color>
    <color name="error">#FFB4AB</color>
    <color name="error_container">#93000A</color>
    <color name="on_error">#690005</color>
    <color name="on_error_container">#FFDAD6</color>
    <color name="on_primary">#371E73</color>
    <color name="on_primary_container">#EADDFF</color>
    <color name="on_surface">#E6E1E5</color>
    <color name="on_surface_variant">#CAC4D0</color>
    <color name="primary">#D0BCFF</color>
    <color name="primary_container">#4F378B</color>
    <color name="surface">#141218</color>
    <color name="surface_variant">#49454F</color>
    <color name="text_hint">#938F99</color>
    <color name="text_primary">#E6E1E5</color>
    <color name="text_secondary">#CAC4D0</color>
    <style name="Theme.Seeq" parent="Theme.Material3.DayNight.NoActionBar"/>
    <style name="Theme.Seeq.BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Seeq.BottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="Widget.Seeq.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.BottomSheet</item>
        <item name="backgroundTint">@color/bottom_sheet_background_night</item>
        <item name="android:elevation">16dp</item>
    </style>
</resources>