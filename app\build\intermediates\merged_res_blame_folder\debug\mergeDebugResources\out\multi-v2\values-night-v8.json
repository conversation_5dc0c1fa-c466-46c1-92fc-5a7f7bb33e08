{"logs": [{"outputFile": "cn.ykload.seeq.app-mergeDebugResources-59:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ccf8b2c27cbeb3cc7666caa413587752\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4169,4244,4355,4444,4545,4652,4759,4858,4965,5068,5195,5283,5407,5509,5611,5727,5829,5943,6071,6187,6309,6445,6565,6699,6819,6931,7523,7640,7764,7894,8016,8154,8288,8404", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4239,4350,4439,4540,4647,4754,4853,4960,5063,5190,5278,5402,5504,5606,5722,5824,5938,6066,6182,6304,6440,6560,6694,6814,6926,7052,7635,7759,7889,8011,8149,8283,8399,8519"}}, {"source": "D:\\Projects\\Seeq\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "35,19,15,16,26,28,27,20,17,18,4,6,8,10,22,24,12,14,25,31,3,5,32,7,9,29,21,30,23,11,13,36,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1986,1086,848,902,1504,1632,1569,1145,965,1021,154,277,402,529,1262,1383,655,780,1448,1810,98,212,1873,344,462,1695,1206,1750,1320,598,714,2040,2088", "endColumns": "53,58,53,62,64,62,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,53,57,66,54,55,59,62,56,65,47,48", "endOffsets": "2035,1140,897,960,1564,1690,1627,1201,1016,1081,207,339,457,593,1315,1443,709,843,1499,1868,149,272,1922,397,524,1745,1257,1805,1378,650,775,2083,2132"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,168,222,285,350,413,476,537,593,658,716,783,843,912,970,1035,1094,1162,1218,1281,1337,1402,1456,1514,1581,1636,1692,1752,1815,1872,1938,1986", "endColumns": "53,58,53,62,64,62,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,53,57,66,54,55,59,62,56,65,47,48", "endOffsets": "104,163,217,280,345,408,471,532,588,653,711,778,838,907,965,1030,1089,1157,1213,1276,1332,1397,1451,1509,1576,1631,1687,1747,1810,1867,1933,1981,2030"}}, {"source": "D:\\Projects\\Seeq\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "11,17,22,31,37,3", "startColumns": "4,4,4,4,4,4", "startOffsets": "454,775,1028,1389,1724,56", "endLines": "15,20,28,35,40,8", "endColumns": "12,12,12,12,12,12", "endOffsets": "769,1022,1363,1718,1975,428"}, "to": {"startLines": "35,40,44,51,56,93", "startColumns": "4,4,4,4,4,4", "startOffsets": "2035,2355,2607,2947,3281,7057", "endLines": "39,43,50,55,59,98", "endColumns": "12,12,12,12,12,12", "endOffsets": "2350,2602,2942,3276,3532,7429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "60,61,62,63,64,65,66,99", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3607,3691,3775,3871,3973,4075,7434", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "3602,3686,3770,3866,3968,4070,4164,7518"}}]}]}