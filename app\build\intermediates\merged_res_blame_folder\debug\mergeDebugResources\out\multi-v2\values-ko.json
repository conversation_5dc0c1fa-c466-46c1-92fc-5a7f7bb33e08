{"logs": [{"outputFile": "cn.ykload.seeq.app-mergeDebugResources-59:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae282b3edfeee1699c61b5e94e80ed2\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "38,39,40,41,42,43,44,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3230,3322,3422,3516,3613,3709,3807,15784", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3317,3417,3511,3608,3704,3802,3902,15880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ccf8b2c27cbeb3cc7666caa413587752\\transformed\\material-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,917,979,1060,1122,1179,1266,1326,1384,1442,1501,1558,1612,1707,1763,1820,1874,1940,2044,2119,2191,2272,2350,2427,2548,2613,2678,2778,2857,2932,2982,3033,3099,3163,3233,3304,3375,3443,3514,3586,3656,3749,3829,3903,3983,4065,4137,4202,4274,4322,4395,4459,4534,4611,4673,4737,4800,4867,4951,5029,5109,5187,5241,5296,5368,5445,5518", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "244,309,373,442,516,595,678,784,859,912,974,1055,1117,1174,1261,1321,1379,1437,1496,1553,1607,1702,1758,1815,1869,1935,2039,2114,2186,2267,2345,2422,2543,2608,2673,2773,2852,2927,2977,3028,3094,3158,3228,3299,3370,3438,3509,3581,3651,3744,3824,3898,3978,4060,4132,4197,4269,4317,4390,4454,4529,4606,4668,4732,4795,4862,4946,5024,5104,5182,5236,5291,5363,5440,5513,5584"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,56,59,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177,181,182,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2879,2944,3008,3077,3151,3907,3990,4096,4584,4637,4699,4780,4996,10700,10787,10847,10905,10963,11022,11079,11133,11228,11284,11341,11395,11461,11565,11640,11712,11793,11871,11948,12069,12134,12199,12299,12378,12453,12503,12554,12620,12684,12754,12825,12896,12964,13035,13107,13177,13270,13350,13424,13504,13586,13658,13723,13795,13843,13916,13980,14055,14132,14194,14258,14321,14388,14472,14550,14630,14708,14762,14975,15275,15352,15493", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,56,59,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177,181,182,184", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "294,2939,3003,3072,3146,3225,3985,4091,4166,4632,4694,4775,4837,5048,10782,10842,10900,10958,11017,11074,11128,11223,11279,11336,11390,11456,11560,11635,11707,11788,11866,11943,12064,12129,12194,12294,12373,12448,12498,12549,12615,12679,12749,12820,12891,12959,13030,13102,13172,13265,13345,13419,13499,13581,13653,13718,13790,13838,13911,13975,14050,14127,14189,14253,14316,14383,14467,14545,14625,14703,14757,14812,15042,15347,15420,15559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\282188e067ace35e7ceeb03ac6735130\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,903,979,1047,1123,1197,1267,1341,1405", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,75,73,69,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,898,974,1042,1118,1192,1262,1336,1400,1514"}, "to": {"startLines": "48,49,50,51,52,57,58,175,176,178,179,183,185,186,187,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4171,4250,4326,4414,4504,4842,4917,14817,14896,15047,15120,15425,15564,15640,15714,15885,15959,16023", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,75,73,69,73,63,113", "endOffsets": "4245,4321,4409,4499,4579,4912,4991,14891,14970,15115,15191,15488,15635,15709,15779,15954,16018,16132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,15196", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,15270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74b732bc14271422d5c17044383b41bc\\transformed\\foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,78", "endOffsets": "132,211"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "16137,16219", "endColumns": "81,78", "endOffsets": "16214,16293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,264,370,474,566,655,761,880,990,1112,1194,1291,1376,1466,1575,1689,1791,1904,2015,2127,2260,2369,2473,2580,2689,2775,2870,2979,3088,3179,3277,3374,3488,3607,3706,3798,3872,3961,4049,4143,4226,4308,4403,4483,4565,4662,4757,4852,4949,5032,5128,5222,5320,5437,5517,5611", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "155,259,365,469,561,650,756,875,985,1107,1189,1286,1371,1461,1570,1684,1786,1899,2010,2122,2255,2364,2468,2575,2684,2770,2865,2974,3083,3174,3272,3369,3483,3602,3701,3793,3867,3956,4044,4138,4221,4303,4398,4478,4560,4657,4752,4847,4944,5027,5123,5217,5315,5432,5512,5606,5697"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5053,5158,5262,5368,5472,5564,5653,5759,5878,5988,6110,6192,6289,6374,6464,6573,6687,6789,6902,7013,7125,7258,7367,7471,7578,7687,7773,7868,7977,8086,8177,8275,8372,8486,8605,8704,8796,8870,8959,9047,9141,9224,9306,9401,9481,9563,9660,9755,9850,9947,10030,10126,10220,10318,10435,10515,10609", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "5153,5257,5363,5467,5559,5648,5754,5873,5983,6105,6187,6284,6369,6459,6568,6682,6784,6897,7008,7120,7253,7362,7466,7573,7682,7768,7863,7972,8081,8172,8270,8367,8481,8600,8699,8791,8865,8954,9042,9136,9219,9301,9396,9476,9558,9655,9750,9845,9942,10025,10121,10215,10313,10430,10510,10604,10695"}}]}]}