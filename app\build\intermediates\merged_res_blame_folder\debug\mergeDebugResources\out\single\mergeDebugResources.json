[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\xml_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\xml\\accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_key.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_key.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_accessibility.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_accessibility.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_bottom_sheet_handle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\bottom_sheet_handle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_warning_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\warning_background.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/drawable_ic_accessibility.xml.flat", "source": "cn.ykload.seeq.app-main-63:/drawable/ic_accessibility.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\layout_bottom_sheet_permission.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\layout\\bottom_sheet_permission.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\layout_dialog_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\layout\\dialog_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\layout_bottom_sheet_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\layout\\bottom_sheet_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_warning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_warning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-debug-61:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\cn.ykload.seeq.app-main-63:\\drawable\\ic_launcher_background.xml"}]