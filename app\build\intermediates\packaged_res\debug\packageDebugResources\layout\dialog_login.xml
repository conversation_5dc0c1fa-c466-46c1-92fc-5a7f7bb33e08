<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 登录码输入框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="请输入6位登录码">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_login_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLength="6"
            android:textSize="18sp"
            android:gravity="center" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 复制指令并跳转QQ群按钮 -->
    <Button
        android:id="@+id/btn_copy_and_jump"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="复制指令并跳转去Q群中发送"
        android:textSize="16sp"
        style="@style/Widget.Material3.Button.OutlinedButton" />

    <!-- 登录按钮 -->
    <Button
        android:id="@+id/btn_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="登录"
        android:textSize="16sp"
        style="@style/Widget.Material3.Button" />

</LinearLayout>
