<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Seeq" parent="Theme.Material3.DayNight.NoActionBar" />

    <!-- 底部模态框主题 -->
    <style name="Theme.Seeq.BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Seeq.BottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="enableEdgeToEdge">true</item>
    </style>

    <!-- 底部模态框样式 -->
    <style name="Widget.Seeq.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.BottomSheet</item>
        <item name="backgroundTint">@color/bottom_sheet_background</item>
        <item name="android:elevation">24dp</item>
        <item name="behavior_peekHeight">0dp</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>

    <!-- 底部模态框圆角形状 -->
    <style name="ShapeAppearance.Seeq.BottomSheet" parent="ShapeAppearance.Material3.Corner.Large">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>

    <!-- 按钮样式 -->
    <style name="Widget.Seeq.Button.Primary" parent="Widget.Material3.Button">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.Button</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <style name="Widget.Seeq.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.Button</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <!-- 按钮圆角形状 -->
    <style name="ShapeAppearance.Seeq.Button" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <!-- 输入框样式 -->
    <style name="Widget.Seeq.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="shapeAppearance">@style/ShapeAppearance.Seeq.TextInputLayout</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="boxStrokeWidthFocused">3dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>

    <!-- 输入框圆角形状 -->
    <style name="ShapeAppearance.Seeq.TextInputLayout" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>

</resources>