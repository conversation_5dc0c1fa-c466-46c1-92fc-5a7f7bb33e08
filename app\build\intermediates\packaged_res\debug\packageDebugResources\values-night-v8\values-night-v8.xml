<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="bottom_sheet_handle">#938F99</color>
    <color name="md_theme_dark_background">#10131C</color>
    <color name="md_theme_dark_error">#F2B8B5</color>
    <color name="md_theme_dark_errorContainer">#8C1D18</color>
    <color name="md_theme_dark_inverseOnSurface">#10131C</color>
    <color name="md_theme_dark_inversePrimary">#6750A4</color>
    <color name="md_theme_dark_inverseSurface">#E6E0E9</color>
    <color name="md_theme_dark_onBackground">#E6E0E9</color>
    <color name="md_theme_dark_onError">#601410</color>
    <color name="md_theme_dark_onErrorContainer">#F9DEDC</color>
    <color name="md_theme_dark_onPrimary">#381E72</color>
    <color name="md_theme_dark_onPrimaryContainer">#EADDFF</color>
    <color name="md_theme_dark_onSecondary">#332D41</color>
    <color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color>
    <color name="md_theme_dark_onSurface">#E6E0E9</color>
    <color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color>
    <color name="md_theme_dark_onTertiary">#492532</color>
    <color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color>
    <color name="md_theme_dark_outline">#938F99</color>
    <color name="md_theme_dark_outlineVariant">#49454F</color>
    <color name="md_theme_dark_primary">#D0BCFF</color>
    <color name="md_theme_dark_primaryContainer">#4F378B</color>
    <color name="md_theme_dark_scrim">#000000</color>
    <color name="md_theme_dark_secondary">#CCC2DC</color>
    <color name="md_theme_dark_secondaryContainer">#4A4458</color>
    <color name="md_theme_dark_shadow">#000000</color>
    <color name="md_theme_dark_surface">#10131C</color>
    <color name="md_theme_dark_surfaceTint">#D0BCFF</color>
    <color name="md_theme_dark_surfaceVariant">#49454F</color>
    <color name="md_theme_dark_tertiary">#EFB8C8</color>
    <color name="md_theme_dark_tertiaryContainer">#633B48</color>
    <color name="success_green">#81C784</color>
    <color name="warning_orange">#FFB74D</color>
    <style name="BottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Dark.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="CustomBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style>
    <style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">28dp</item>
        <item name="cornerSizeTopLeft">28dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style>
    <style name="PermissionBottomSheetDialogTheme" parent="Theme.Material3.DynamicColors.Dark.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/PermissionBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="PermissionBottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">?attr/colorSurface</item>
    </style>
    <style name="Theme.Seeq" parent="Theme.Material3.DynamicColors.Dark">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
</resources>