<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽手柄 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:alpha="0.4" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_accessibility"
            android:tint="@color/primary"
            android:contentDescription="无障碍权限图标" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="需要无障碍权限"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:tint="@color/text_secondary"
            android:contentDescription="关闭"
            android:padding="8dp" />

    </LinearLayout>

    <!-- 权限说明卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/primary_container">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 权限用途标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="权限用途说明"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/on_primary_container" />

            <!-- 权限用途列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 用途1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginTop="2dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/on_primary_container" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="自动导入EQ均衡器设置"
                        android:textSize="14sp"
                        android:textColor="@color/on_primary_container" />

                </LinearLayout>

                <!-- 用途2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginTop="2dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/on_primary_container" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="操作欢律App或系统蓝牙设置"
                        android:textSize="14sp"
                        android:textColor="@color/on_primary_container" />

                </LinearLayout>

                <!-- 用途3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginTop="2dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/on_primary_container" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="提供便捷的音频体验"
                        android:textSize="14sp"
                        android:textColor="@color/on_primary_container" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 重要提示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:background="@drawable/warning_background">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/ic_warning"
            android:tint="@color/error" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="若Seeq后台被杀，则需要重新设置权限"
            android:textSize="14sp"
            android:textColor="@color/error"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 去设置按钮 -->
        <Button
            android:id="@+id/btn_go_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:minHeight="56dp"
            android:text="去设置"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button"
            app:icon="@drawable/ic_settings"
            app:iconGravity="start" />

        <!-- 我已设置按钮 -->
        <Button
            android:id="@+id/btn_already_set"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:minHeight="56dp"
            android:text="我已设置"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            app:icon="@drawable/ic_check"
            app:iconGravity="start" />

    </LinearLayout>

</LinearLayout>
